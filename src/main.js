import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css'
import './assets/fontIcon/iconfont.css'

import Element from 'element-ui'
// 富文本
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

// 数据字典
import dict from './components/Dict'

// 权限指令
import checkPer from '@/utils/permission'
import permission from './components/Permission'
import './assets/styles/element-variables.scss'
// global css
import './assets/styles/index.scss'
import './assets/css/scss/form.scss'
import './assets/css/comm.scss'

// 代码高亮
import VueHighlightJS from '@/components/vueHighlightJS'
import 'highlight.js/styles/atom-one-dark.css'

import App from './App'
import store from './store'
import router from './router/routers'

import './assets/icons' // icon
import './router/index' // permission control
import 'echarts-gl'
import globalUtil from './utils/global'

// 表单设计
import FormMaking from '@/../lib/form-making-advanced'
import '@/../lib/form-making-advanced/dist/FormMaking.css'
// 表单设计器
// import FormMaking from '@hzwangda/form-making'
// import '@hzwangda/form-making/dist/FormMaking.css'
// import '@hzwangda/form-making/src/assets/font/iconfont'

// 表单设计富文本编辑器，不支持IE10
import VueEditor from '@/../lib/form-making-advanced/dist/Editor.common'
Vue.use(VueEditor)

import Moment from 'moment'
Vue.prototype.moment = Moment

import Vant from 'vant'
import 'vant/lib/index.css'
Vue.use(Vant)
// vxe表格
import 'xe-utils'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'

// 滚动条
import GeminiScrollbar from 'vue-gemini-scrollbar'
Vue.use(GeminiScrollbar)

// 导入excel插件
import XLSX from 'xlsx'
// import axios from 'axios'
// import { PreviewPdf } from '@/utils/previewPdf'
Vue.prototype.XLSX = XLSX

Vue.use(VXETable)
Vue.use(FormMaking)

Vue.use(checkPer)
Vue.use(globalUtil)

Vue.use(VueHighlightJS)
Vue.use(mavonEditor)
Vue.use(permission)
Vue.use(dict)
Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
/*
const originalOpen = window.open
window.open = async function(url, target, features) {
  if (await isPdf(url)) {
    return handlePdf(url, (pdfurl) => originalOpen.call(this, pdfurl, target, features))
  }
  return originalOpen(url, target, features)
}
*/
/* import { getToken } from '@/utils/auth'
async function isPdf(url) {
  if (/\.pdf(\?.*)?$/i.test(url)) return true
  if (!url.startsWith('http')) return false
  return axios.head(url, {
    headers: {
      authorization: getToken()
    }
  })
    .then(res => res.headers['content-type'].includes('application/pdf'))
}
function handlePdf(url, callback) {
  const watermark = store.getters.user.name
  axios.get(url, {
    responseType: 'blob'
  }).then(blobRes => {
    PreviewPdf(blobRes.data, watermark).then(res => {
      const URL = window.URL || window.webkitURL
      const pdfurl = URL.createObjectURL(new Blob([res], { type: 'application/pdf;charset=utf-8' }))
      return callback(pdfurl)
    })
  })
}

// a标签下载
const func = async(e) => {
  const attr = 'data-pdf-processed'
  const aTag = e.target.closest('a')
  if (!aTag || !aTag.hasAttribute('href') || aTag.hasAttribute(attr)) return
  const href = aTag.getAttribute('href')
  const filename = aTag.getAttribute('download')
  if (filename.endsWith('.pdf') || await isPdf(href)) {
    e.preventDefault()
    e.stopImmediatePropagation()

    handlePdf(href, (pdfurl) => {
      aTag.setAttribute('href', pdfurl)
      aTag.setAttribute(attr, true)
      aTag.click()
      setTimeout(() => {
        aTag.remove()
        URL.revokeObjectURL(aTag.href)
      }, 0)
    })
  }
}*/
// document.addEventListener('click', func, true)
