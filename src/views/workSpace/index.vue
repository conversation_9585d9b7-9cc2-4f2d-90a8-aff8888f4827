<template>
  <div style="width:100%;height:100%">
    <versionOld v-if="versionData===0" />
    <versionNew v-if="versionData===1" />

    <!-- 右下角图片浮窗 -->
    <template v-if="showTemplateFloatWindow">
      <div v-if="showFloatWindow" class="float-window" :style="{width:FloatWindowWidht+'px',height:FloatWindowHeight+'px'}">
        <a href="https://oa.zjedu.gov.cn/aigov-service/api/localStorage/previewFile/2950590619402240" target="_blank">
          <img src="https://oa.zjedu.gov.cn/aigov-service/api/localStorage/previewFile/2950590619402240" alt="浮窗图片" class="float-image">
        </a>
        <button class="close-btn" @click.stop="closeFloatWindow">×</button>
      </div>
    </template>
  </div>
</template>
<script>
import versionOld from './versionOld'
import versionNew from './versionNew'
export default {
  components: { versionOld, versionNew },
  data() {
    return {
      versionData: null,
      changeCount: 1,
      showFloatWindow: true,
      showTemplateFloatWindow: window.g.showTemplateFloatWindow,
      FloatWindowWidht: window.g.FloatWindowWidht || 184,
      FloatWindowHeight: window.g.FloatWindowHeight || 252
    }
  },
  watch: {
    '$store.state.settings.versionStute'(newVal, oldVal) {
      this.versionData = newVal
    }
  },
  created() {
    this.versionData = this.$store.state.settings.versionStute
  },
  methods: {
    closeFloatWindow() {
      this.showFloatWindow = false
    }
  }
}
</script>
<style scoped>
.float-window {
  position: fixed;
  bottom: 20px;
  right: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: white;
  z-index: 1000;
}

.float-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.close-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}
</style>
