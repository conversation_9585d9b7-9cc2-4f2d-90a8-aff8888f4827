<template>
  <div style="width:100%;height:100%">
    <versionOld v-if="versionData===0" />
    <versionNew v-if="versionData===1" />

    <!-- 右下角图片浮窗 -->
    <template v-if="showTemplateFloatWindow">
      <div
        v-if="showFloatWindow"
        class="float-window"
        :style="{
          width: FloatWindowWidht + 'px',
          height: FloatWindowHeight + 'px',
          left: floatWindowPosition.x + 'px',
          top: floatWindowPosition.y + 'px'
        }"
        @mousedown="startDrag"
      >
        <a href="https://oa.zjedu.gov.cn/aigov-service/api/localStorage/previewFile/2950590619402240" target="_blank">
          <img src="https://oa.zjedu.gov.cn/aigov-service/api/localStorage/previewFile/2950590619402240" alt="浮窗图片" class="float-image">
        </a>
        <button class="close-btn" @click.stop="closeFloatWindow">×</button>
      </div>
    </template>
  </div>
</template>
<script>
import versionOld from './versionOld'
import versionNew from './versionNew'
export default {
  components: { versionOld, versionNew },
  data() {
    return {
      versionData: null,
      changeCount: 1,
      showFloatWindow: true,
      showTemplateFloatWindow: window.g.showTemplateFloatWindow,
      FloatWindowWidht: window.g.FloatWindowWidht || 184,
      FloatWindowHeight: window.g.FloatWindowHeight || 252,
      // 拖拽相关数据
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      floatWindowPosition: {
        x: window.innerWidth - (window.g.FloatWindowWidht || 184) - 20, // 默认右下角
        y: window.innerHeight - (window.g.FloatWindowHeight || 252) - 20
      }
    }
  },
  watch: {
    '$store.state.settings.versionStute'(newVal, oldVal) {
      this.versionData = newVal
    }
  },
  created() {
    this.versionData = this.$store.state.settings.versionStute
  },
  mounted() {
    // 监听窗口大小变化，调整浮窗位置
    window.addEventListener('resize', this.handleWindowResize)
    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseup', this.handleMouseUp)
  },
  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('resize', this.handleWindowResize)
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseup', this.handleMouseUp)
  },
  methods: {
    closeFloatWindow() {
      this.showFloatWindow = false
    },
    // 开始拖拽
    startDrag(event) {
      // 如果点击的是关闭按钮或链接，不启动拖拽
      if (event.target.classList.contains('close-btn') || event.target.tagName === 'A' || event.target.tagName === 'IMG') {
        return
      }

      this.isDragging = true
      this.dragOffset.x = event.clientX - this.floatWindowPosition.x
      this.dragOffset.y = event.clientY - this.floatWindowPosition.y

      // 防止选中文本
      event.preventDefault()
    },
    // 处理鼠标移动
    handleMouseMove(event) {
      if (!this.isDragging) return

      let newX = event.clientX - this.dragOffset.x
      let newY = event.clientY - this.dragOffset.y

      // 边界检测，确保浮窗不会拖出屏幕
      const maxX = window.innerWidth - this.FloatWindowWidht
      const maxY = window.innerHeight - this.FloatWindowHeight

      newX = Math.max(0, Math.min(newX, maxX))
      newY = Math.max(0, Math.min(newY, maxY))

      this.floatWindowPosition.x = newX
      this.floatWindowPosition.y = newY
    },
    // 结束拖拽
    handleMouseUp() {
      this.isDragging = false
    },
    // 处理窗口大小变化
    handleWindowResize() {
      // 确保浮窗在窗口调整后仍在可见区域内
      const maxX = window.innerWidth - this.FloatWindowWidht
      const maxY = window.innerHeight - this.FloatWindowHeight

      this.floatWindowPosition.x = Math.max(0, Math.min(this.floatWindowPosition.x, maxX))
      this.floatWindowPosition.y = Math.max(0, Math.min(this.floatWindowPosition.y, maxY))
    }
  }
}
</script>
<style scoped>
.float-window {
  position: fixed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: white;
  z-index: 1000;
  cursor: move;
  user-select: none;
  transition: box-shadow 0.2s ease;
}

.float-window:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.float-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.close-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}
</style>
