<template>
  <div class="app-container">
    <div class="top_wrap">
      <div class="tool_bar">
        <el-form :inline="true" :model="queryParams" label-width="90px" class="search-form-inline">
          <el-form-item label="年份：">
            <el-date-picker
              v-model="queryParams.year"
              style="width: 180px"
              type="year"
              format="yyyy"
              value-format="yyyy"
              placeholder="请选择年份"
              @change="handleQuery"
            />
          </el-form-item>
          <el-form-item label="关键字：">
            <el-input v-model="queryParams.name" placeholder="请输入关键字" clearable />
          </el-form-item>
          <el-form-item style="margin-left: 10px">
            <el-button type="primary" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="bottom_wrap">
      <div class="left_tabs flex">
        <div class="left_tab">
          <div v-for="item in leftTabsList" :key="item.id" :class="item.id==leftaActiveId?'active':''" class="column_item" @click="leftaActiveId = item.id,toggleLeftTab(item)">
            <span>{{ item.label }}</span>
          </div>
          <!-- <div class="column_item" @click="createdClick">新建</div> -->
        </div>
        <div class="left_btns">
          <div class="left_btn gird" style="padding-top:20px" @click="taskBox()"><i class="el-icon-notebook-1" /><span class="left_btn_label">任务清单</span></div>
        </div>
      </div>
      <div class="right_content">
        <!-- 任务清单 -->
        <div v-show="taskBoxShow" class="table_area" style="padding:16px">
          <!-- <div class="taskBox">
            <el-row>

            </el-row>
            <div class="taskCard">
              123
            </div>
          </div> -->
          <el-row v-loading="taskLoading" :gutter="20">
            <el-col v-for="item in taskListData" :key="item.id" :span="6">
              <div class="card">
                <div class="card_body">
                  <div class="title">
                    {{ item.name }}
                  </div>
                  <div class="tasks">
                    <div class="task_item">一级任务数:<span class="task_item_num">{{ item.firstLevelCount?item.firstLevelCount:0 }}</span></div>
                    <div class="task_item">二级任务数:<span class="task_item_num">{{ item.secondLevelCount?item.secondLevelCount:0 }}</span></div>
                    <div class="task_item">三级任务数:<span class="task_item_num">{{ item.thirdLevelCount?item.thirdLevelCount:0 }}</span></div>
                  </div>
                  <div class="main">
                    <p><span>创建时间：</span>{{ moment(item.createTime).format('YYYY-MM-DD') }}</p>
                    <p><span>反馈通知：</span>
                      <el-tooltip v-if="item.feedbackNotice" effect="light" :content="item.feedbackNotice" placement="top-start" popper-class="feedbackNoticePopper">
                        <span class="oneline-limit" style="display: inline-block;width: calc(100% - 70px);vertical-align: bottom;">{{ item.feedbackNotice }}</span>
                      </el-tooltip>
                    </p>
                    <p><span>状态：</span><el-tag type="success" size="mini">进行中</el-tag></p>
                    <!-- <p style="display: flex;">
                      <span>进度：</span>
                      <span style="flex: 1;"><el-progress :percentage="item.progress?item.progress:0" :stroke-width="10" /></span>
                    </p> -->
                  </div>
                </div>
                <div class="card_toolbox">
                  <el-button v-if="item.buttonPermissions.opinion" type="primary" size="small" round @click="routerLink('workRecord',{catalogId:item.catalogId,editType:'responsible'})">任务确认</el-button>
                  <el-button type="primary" size="small" round @click="routerLink('workRecord',{catalogId:item.catalogId,editType:'workRecord',csz:item.buttonPermissions && item.buttonPermissions.csz?item.buttonPermissions.csz:false})">工作记实</el-button>
                  <el-button type="primary" size="small" round @click="routerLink('taskList',{catalogId:item.catalogId,firstLevelCount:item.firstLevelCount,secondLevelCount:item.secondLevelCount,thirdLevelCount:item.thirdLevelCount,viewAll:item.buttonPermissions.viewAll,})">任务总览</el-button>
                  <!-- <el-button type="primary" size="small" round @click="routerLink('workRecord',{catalogId:item.catalogId,editType:'responsible'})">任务确认</el-button>
                  <el-button type="primary" size="small" round @click="routerLink('workRecord',{catalogId:item.catalogId,editType:'workRecord'})">工作记实</el-button>
                  <el-button type="primary" size="small" round @click="routerLink('taskList',{catalogId:item.catalogId})">任务总览</el-button> -->
                </div>
              </div>
            </el-col>
          </el-row>
          <!-- v-show="taskQueryParams.total>0" -->
          <vxe-pager
            ref="table"
            perfect
            row-id="id"
            :current-page.sync="taskQueryParams.pageNo"
            :page-size.sync="taskQueryParams.size"
            :total="taskQueryParams.total"
            :page-sizes="[10, 20, 50]"
            :layouts="['Total', 'Sizes', 'PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'FullJump']"
            @page-change="taskHandlePageChange"
          />
        </div>
        <!-- 任务清单 -->
        <!-- 流程列表 -->
        <div v-show="!taskBoxShow" class="table_area" style="padding:16px">
          <!--表格渲染-->
          <vxe-table v-loading="loading" class="vxe_table" border resizable auto-resize stripe highlight-hover-row :data="listData">
            <vxe-column type="seq" title="序号" align="center" width="60" />
            <vxe-table-column show-overflow title="名称">
              <template #default="{ row }">
                <el-link @click="handleBtClick(row.processInstanceId)">
                  <p class="pStyle">{{ row.title }}</p></el-link>
              </template>
            </vxe-table-column>
            <vxe-table-column title="当前办理人" width="150">
              <template #default="{ row }">
                <span>{{ row.currentHandler }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column title="申请人" width="300">
              <template #default="{ row }">
                <span>{{ row.createBy }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column show-overflow title="创建时间" width="150">
              <template #default="{ row }">
                <span>{{ moment(row.createDate).format('YYYY-MM-DD') }}</span>
              </template>
            </vxe-table-column>
          </vxe-table>
          <vxe-pager
            v-show="queryParams.total>0"
            ref="table"
            perfect
            row-id="id"
            :current-page.sync="queryParams.pageNo"
            :page-size.sync="queryParams.size"
            :total="queryParams.total"
            :page-sizes="[10, 20, 50]"
            :layouts="['Total', 'Sizes', 'PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'FullJump']"
            @page-change="handlePageChange"
          />
        </div>
      <!-- 流程列表 -->
      </div>
      <!-- beign 表单-->
      <el-dialog :close-on-click-modal="false" :before-close="hideFormDialog" :visible.sync="isFormDialogShow" title="新增任务" width="540px">
        <div>
          <el-form ref="form" :model="createForm" label-width="100px">
            <el-form-item label="任务类型：">
              <el-select v-model="createForm.taskSource" placeholder="教育任务" clearable style="width: 100%;">
                <el-option
                  v-for="item in tabsList2"
                  :key="item.id"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="层级结构：">
              <el-select v-model="classesIds" placeholder="全部" clearable style="width: 80%;">
                <el-option
                  v-for="item in classOptions"
                  :key="item.classId"
                  :label="item.name"
                  :value="item.classId"
                />
              </el-select>
              <el-button type="primary" style="margin-left:10px" @click="routerLink('/db/catalog')">维护</el-button>
            </el-form-item>
            <el-form-item label="任务名称：">
              <el-input v-model="createForm.name" style="width: 100%;" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="任务编号：">
              <el-input-number v-model="createForm.taskNo" :min="1" :max="10000" clearable style="width: 100%;" @change="handleChange" />
            </el-form-item>
            <el-form-item label="任务详情：">
              <el-input v-model="createForm.content" style="width: 100%;" placeholder="请输入" type="textarea" :rows="2" clearable />
            </el-form-item>
            <el-form-item label="牵头处室：">
              <treeselect
                v-model="deptIds"
                :options="depts"
                :multiple="true"
                style="width: 100%"
                placeholder="选择部门"
              />
            </el-form-item>
            <el-form-item label="协同处室：">
              <treeselect
                v-model="assistDeptIds"
                :options="depts"
                :multiple="true"
                style="width: 100%"
                placeholder="选择部门"
              />
            </el-form-item>
          </el-form>
        </div>

        <div slot="footer">
          <el-button @click="hideFormDialog">取消</el-button>
          <el-button type="primary" @click="doFormSave">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import crudTmTask from '@/api/aigov/taskmanagement/tmTask'
import { getSelectDept } from '@/api/taskmanagement/sevenList'
import classApi from '@/api/aigov/taskmanagement/tmClass'
import pickUser from '@/components/pickUser/index.js'// 混入选人组件
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import tmCataLogApi from '@/api/taskmanagement/tmCataLog'
import { page } from '@/api/taskmanagement'
import { webPageRouter } from '@/api/information'
export default {
  name: 'TmTask',
  dicts: [],
  components: { Treeselect },
  mixins: [pickUser],
  data() {
    return {
      listData: [],
      loading: true,
      queryParams: {
        pageNo: 1,
        size: 10,
        year: null,
        name: ''
      },
      tabsList2: [
        { id: 8, label: '省委任务' },
        { id: 9, label: '省政府任务' },
        { id: 7, label: '厅重大任务' }
      ],
      leftaActiveId: 1, // 左侧tab活跃id
      leftTabsList: [{ id: 1, label: '全部', status: '' }, { id: 2, label: '待办', status: 0 }, { id: 3, label: '已办', status: 1 }],
      classOptions: [],
      deptOptions: [],
      associatedTaskOptions: [],
      classesIds: '',
      deptIds: [],
      assistDeptIds: [],
      flag: true,
      treeData: null,
      headDepartOptions: [],
      isFormDialogShow: false,
      createForm: {
        type: 1,
        classes: [],
        name: '',
        taskNo: null,
        taskSource: '',
        depts: [],
        assistDepts: [],
        process: null,
        contacts: ''
      },
      choicContanct: [],
      isPickDialog: false, // 选择人员dialog
      depts: [],
      taskBoxShow: true,
      taskQueryParams: {
        pageNo: 1,
        size: 10
      },
      taskListData: [],
      taskLoading: false
    }
  },
  computed: {
    ...mapGetters(['user'])
  },
  created() {
    if (this.$route.query.leftaActiveId) {
      this.leftaActiveId = Number(this.$route.query.leftaActiveId)
      this.toggleLeftTab(this.leftTabsList.find(item => item.id === this.leftaActiveId))
    } else {
      this.taskBox()
    }
    // this.loadListData()
    this.getClassesAll()
    this.getDepts()
  },
  methods: {
    loadListData() {
      this.loading = true
      this.listData = []
      page(this.queryParams).then(res => {
        this.listData = res.content
        this.queryParams.total = res.totalElements
        this.loading = false
      })
    },
    doFormSave() {
      const assistDepts = []
      this.assistDeptIds.forEach(function(data) {
        const dept = { id: data }
        assistDepts.push(dept)
      })
      this.createForm.assistDepts = assistDepts
      const classesIds = []
      const dept = { classId: this.classesIds }
      classesIds.push(dept)
      this.createForm.classes = classesIds
      const deptIds = []
      this.deptIds.forEach(function(data) {
        const dept = { id: data }
        deptIds.push(dept)
      })
      this.createForm.depts = deptIds
      // if (this.choicContanct.length > 0 && this.choicContanct[0].userName) {
      //   this.createForm.contacts = this.choicContanct[0].userName
      // } else {
      //   this.createForm.contacts = ''
      // }
      this.createForm.contacts = this.user.userName
      crudTmTask.add(this.createForm).then(res => {
        this.clearData()
        this.$notify({
          type: 'success',
          message: '添加成功！'
        })
        this.handleQuery()
        this.isFormDialogShow = false
      })
    },
    clearData() {
      this.classesIds = ''
      this.deptIds = []
      this.assistDeptIds = []
      this.createForm.classes = null
      this.createForm.name = ''
      this.createForm.taskSource = ''
      this.createForm.depts = []
      this.createForm.assistDepts = []
      this.createForm.process = null
      this.createForm.contacts = ''
      this.choicContanct = []
    },
    handleQuery() {
      this.queryParams.pageNo = 1
      this.loadListData()
    },
    // 左侧切换
    toggleLeftTab(item) {
      this.taskBoxShow = false
      if (this.leftaActiveId === 4) return
      this.queryParams.status = item.status
      this.queryParams.pageNo = 1
      this.loadListData()
    },
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pageNo = currentPage
      this.queryParams.size = pageSize
      this.loadListData()
    },
    hideFormDialog() {
      this.clearData()
      this.isFormDialogShow = false
    },
    createdClick() {
      this.isFormDialogShow = true
    },
    leaveClick() {
      if (!this.isZeroToHundred(this.createForm.process)) {
        this.$notify({
          type: 'warning',
          message: '请输入0~100的数字！'
        })
        this.createForm.process = null
        return
      }
    },
    isZeroToHundred(str) {
      return /^[1-9][0-9]?$|100$|0$/.test(str)
    },
    handleChange(value) {
      console.log(value)
    },
    getClassesAll() {
      classApi.getClassesAll().then(res => {
        this.classOptions = res
      })
    },
    handleBtClick(id) {
      webPageRouter({ processInstanceId: id }).then(res => {
        const routeData = this.$router.resolve({
          path: '/handle',
          query: res.query
        })
        window.open(routeData.href, '_blank')
      })
    },
    // 打开选择人员Dialog
    showPickUserDialog() {
      this.isPickDialog = true
      this.$refs.selectPickUser.blur()
    },
    // 提交选择人员
    submitPickUser(data) {
      this.choicContanct = data
    },
    getDepts() {
      const data = {
        DeptId: 10000,
        otherDepts: [10038, 10051, 10050, 10064, 10065, 10067, 10066]
      }
      getSelectDept(data).then(res => {
        this.depts = res
      })
    },
    taskBox() {
      this.leftaActiveId = null
      this.taskBoxShow = true
      this.taskloadListData()
    },
    taskHandlePageChange({ currentPage, pageSize }) {
      this.taskQueryParams.pageNo = currentPage
      this.taskQueryParams.size = pageSize
      this.taskloadListData()
    },
    taskloadListData() {
      this.taskLoading = true
      tmCataLogApi.queryPage(this.taskQueryParams).then(res => {
        this.taskListData = res.content
        this.taskQueryParams.total = res.totalElements
      })
      this.taskLoading = false
    }
  }
}
</script>
  <style lang='scss' scoped>
  @import '@/assets/css/scss/overview.scss';
  .app-container {
    padding: 15px;
    height: 100%;
    .card{
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      border-radius: 4px;
      border: 1px solid #ebeef5;
      background-color: #fff;
      overflow: hidden;
      color: #303133;
      transition: .3s;
      margin-bottom: 20px;
      .card_body{
        padding: 20px;
        .title{
          font-size: 16px;
          margin-bottom: 12px;
          height: 37px;
          font-weight: bold;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .tasks{
          display: flex;
          justify-content: space-between;
          margin-bottom:12px ;
          .task_item{
            font-size: 13px;
            color: #303133;
            .task_item_num{
              color: #2A78FF;
              font-weight: 500;
            }
          }
        }
        .main{
          font-size: 13px;
          p{
            label{
              color: #303133;
              // font-weight: 400;
            }
            &:not(:last-child) {
            margin-bottom:10px ;
          }
          }
        }
      }
      .card_toolbox{
        text-align: right;
        padding: 10px 20px;
        border-top: 1px solid #ebeef5;
        box-sizing: border-box;
      }
    }
  }
  .pStyle{
    // height: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .bottom_wrap{
    height: calc(100% - 88px);
  }

  </style>

  <style lang="scss">
   .feedbackNoticePopper{
    max-width: 400px;
  }
</style>
